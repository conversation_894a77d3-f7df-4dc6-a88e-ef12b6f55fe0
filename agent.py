import sys
from collections import deque

# Constants
ORTHOGONAL_DIRECTIONS = [(0, 1), (0, -1), (1, 0), (-1, 0)]
ALL_DIRECTIONS = [(0, 1), (0, -1), (1, 0), (-1, 0), (1, 1), (1, -1), (-1, 1), (-1, -1)]
MAX_THROW_RANGE = 4
SPLASH_DAMAGE = 30

def manhattan_distance(x1, y1, x2, y2):
    return abs(x1 - x2) + abs(y1 - y2)

def get_adjacent_tiles(x, y, width, height):
    return [(x + dx, y + dy) for dx, dy in ORTHOGONAL_DIRECTIONS
            if 0 <= x + dx < width and 0 <= y + dy < height]

def get_splash_area(x, y, width, height):
    return [(x + dx, y + dy) for dx, dy in [(0, 0)] + ALL_DIRECTIONS
            if 0 <= x + dx < width and 0 <= y + dy < height]

def can_move_to_tile(x, y, grid, agents):
    """Check if a tile is passable (no cover, no other agents)."""
    # Check for cover
    tile = next((t for t in grid if t['x'] == x and t['y'] == y), None)
    if tile and tile['tile_type'] > 0:
        return False
    # Check for other agents
    for agent in agents:
        if agent['x'] == x and agent['y'] == y:
            return False
    return True

def find_reachable_tiles(start_x, start_y, grid, agents, width, height, max_distance=None):
    """Find all tiles reachable from start position using BFS."""
    visited = set()
    reachable = {}  # (x, y) -> distance
    queue = deque([(start_x, start_y, 0)])
    visited.add((start_x, start_y))
    reachable[(start_x, start_y)] = 0

    while queue:
        x, y, dist = queue.popleft()

        if max_distance and dist >= max_distance:
            continue

        for nx, ny in get_adjacent_tiles(x, y, width, height):
            if (nx, ny) not in visited and can_move_to_tile(nx, ny, grid, agents):
                visited.add((nx, ny))
                reachable[(nx, ny)] = dist + 1
                queue.append((nx, ny, dist + 1))

    return reachable

def is_safe_splash_target(target_x, target_y, my_agents, width, height):
    splash_area = get_splash_area(target_x, target_y, width, height)
    return not any((agent['x'], agent['y']) in splash_area for agent in my_agents)

def find_best_splash_targets(agent_x, agent_y, enemy_agents, my_agents, width, height):
    best_targets = []

    for target_x in range(max(0, agent_x - MAX_THROW_RANGE), min(width, agent_x + MAX_THROW_RANGE + 1)):
        for target_y in range(max(0, agent_y - MAX_THROW_RANGE), min(height, agent_y + MAX_THROW_RANGE + 1)):
            distance = manhattan_distance(agent_x, agent_y, target_x, target_y)
            if distance > MAX_THROW_RANGE or not is_safe_splash_target(target_x, target_y, my_agents, width, height):
                continue

            splash_area = get_splash_area(target_x, target_y, width, height)
            enemies_hit = [enemy for enemy in enemy_agents if (enemy['x'], enemy['y']) in splash_area]

            if enemies_hit:
                total_enemy_wetness = sum(enemy.get('wetness', 0) for enemy in enemies_hit)
                elimination_potential = sum(1 for enemy in enemies_hit if enemy.get('wetness', 0) + SPLASH_DAMAGE >= 100)

                # Calculate safety penalty - heavily penalize throws near friendly agents
                safety_penalty = 0
                min_friendly_distance = float('inf')
                for agent in my_agents:
                    agent_distance = manhattan_distance(target_x, target_y, agent['x'], agent['y'])
                    min_friendly_distance = min(min_friendly_distance, agent_distance)

                    # Strong penalty for throws near friendlies (even if not hitting directly)
                    if agent_distance <= 2:  # Within 2 tiles of splash center
                        safety_penalty += 10000  # Massive penalty
                    elif agent_distance <= 3:  # Within 3 tiles
                        safety_penalty += 1000   # Large penalty

                best_targets.append({
                    'target_pos': (target_x, target_y),
                    'distance': distance,
                    'enemies_hit': enemies_hit,
                    'total_damage': len(enemies_hit) * SPLASH_DAMAGE,
                    'enemy_count': len(enemies_hit),
                    'elimination_potential': elimination_potential,
                    'total_enemy_wetness': total_enemy_wetness,
                    'safety_penalty': safety_penalty,
                    'min_friendly_distance': min_friendly_distance
                })

    # Sort with safety as highest priority, then effectiveness
    best_targets.sort(key=lambda x: (x['safety_penalty'], -x['elimination_potential'], -x['enemy_count'], -x['total_enemy_wetness'], x['distance']))

    # Debug output for splash bomb targeting
    if best_targets:
        best = best_targets[0]
        print(f"DEBUG: Best splash target at {best['target_pos']} hits {best['enemy_count']} enemies, "
              f"safety_penalty={best['safety_penalty']}, min_friendly_dist={best['min_friendly_distance']:.1f}", file=sys.stderr)

    return best_targets

def detect_jail_cells(grid, width, height):
    cells = []
    cover_tiles = {(t['x'], t['y']) for t in grid if t['tile_type'] > 0}

    for x in range(0, width - 4):
        for y in range(0, height - 4):
            # Check for 5x5 cover pattern (walls with 3x3 interior)
            walls = [(x + i, y) for i in range(5)] + [(x + i, y + 4) for i in range(5)] + \
                   [(x, y + i) for i in range(1, 4)] + [(x + 4, y + i) for i in range(1, 4)]

            if all(pos in cover_tiles for pos in walls):
                interior = [(x + 1 + i, y + 1 + j) for i in range(3) for j in range(3)]
                cells.append({
                    'bounds': (x, y, x + 4, y + 4),
                    'interior': interior,
                    'center': (x + 2, y + 2)
                })
    return cells

def find_agent_cell(agent, cells):
    return next((cell for cell in cells if (agent['x'], agent['y']) in cell['interior']), None)

def find_safe_throw_position_in_cell(enemies_in_cell, cell):
    center_x, center_y = cell['center']
    splash_area = get_splash_area(center_x, center_y, 20, 20)

    for pos_x, pos_y in cell['interior']:
        all_enemies_hit = all((enemy['x'], enemy['y']) in splash_area for enemy in enemies_in_cell)
        agent_safe = (pos_x, pos_y) not in splash_area

        if all_enemies_hit and agent_safe:
            return (pos_x, pos_y), (center_x, center_y)
    return None, None

def find_cells_with_only_enemies(cells, enemy_agents, my_agents):
    target_cells = []
    for cell in cells:
        enemies_in_cell = [e for e in enemy_agents if (e['x'], e['y']) in cell['interior']]
        friendlies_in_cell = [a for a in my_agents if (a['x'], a['y']) in cell['interior']]

        if enemies_in_cell and not friendlies_in_cell:
            target_cells.append({
                'cell': cell,
                'enemies': enemies_in_cell,
                'enemy_count': len(enemies_in_cell)
            })
    return target_cells

# Helper functions for command building and strategy
def build_agent_command(agent_id, actions, status_msg=""):
    command = f"{agent_id}"
    for action in actions:
        command += f";{action}"
    if status_msg:
        command += f";MESSAGE {status_msg}"
    return command

def get_agent_init_data(agent_id, agents_init):
    return next(init_a for init_a in agents_init if init_a['agent_id'] == agent_id)

def create_target_info(target_pos, distance, enemies_hit, total_enemy_wetness):
    return {
        'target_pos': target_pos,
        'distance': distance,
        'enemies_hit': enemies_hit,
        'total_damage': len(enemies_hit) * SPLASH_DAMAGE,
        'enemy_count': len(enemies_hit),
        'elimination_potential': len(enemies_hit),
        'total_enemy_wetness': total_enemy_wetness
    }

def try_splash_attack(agent, enemy_agents, my_agents, width, height, taken_enemies):
    """Try to execute a splash bomb attack. Returns (success, actions, enemies_taken)"""
    agent_x, agent_y = agent['x'], agent['y']
    current_throw_targets = find_best_splash_targets(agent_x, agent_y, enemy_agents, my_agents, width, height)

    available_targets = []
    for target in current_throw_targets:
        enemy_ids = {enemy['agent_id'] for enemy in target['enemies_hit']}
        if not enemy_ids.intersection(taken_enemies):
            available_targets.append(target)

    if available_targets:
        best_target = available_targets[0]
        target_x, target_y = best_target['target_pos']
        throw_distance = manhattan_distance(agent_x, agent_y, target_x, target_y)

        if throw_distance <= MAX_THROW_RANGE:
            actions = [f"THROW {target_x} {target_y}"]
            enemies_taken = {enemy['agent_id'] for enemy in best_target['enemies_hit']}
            print(f"DEBUG: Agent {agent['agent_id']} throwing splash bomb at {target_x},{target_y} "
                  f"(safety_penalty={best_target.get('safety_penalty', 0)}, "
                  f"hits {len(best_target['enemies_hit'])} enemies)", file=sys.stderr)
            return True, actions, enemies_taken

    return False, [], set()

def try_shooting_attack(agent, enemy_agents, grid, width, height, agents_init, taken_enemies):
    if agent['cooldown'] != 0:
        return False, [], set()

    agent_x, agent_y = agent['x'], agent['y']
    init_agent = get_agent_init_data(agent['agent_id'], agents_init)
    target_enemy = find_best_target_enemy(
        agent_x, agent_y, enemy_agents, grid, width, height,
        init_agent['optimal_range'], init_agent['soaking_power'], taken_enemies
    )

    if target_enemy:
        effective_wetness, _, distance, _ = calculate_effective_wetness(
            agent_x, agent_y, target_enemy, grid, width, height,
            init_agent['soaking_power'], init_agent['optimal_range']
        )

        if effective_wetness > 0 and distance <= 2 * init_agent['optimal_range']:
            return True, [f"SHOOT {target_enemy['agent_id']}"], {target_enemy['agent_id']}

    return False, [], set()

def process_focus_fire_agent(agent, closest_enemy, safe_positions, grid, width, height, agents_init, my_agents):
    """Process a single agent for coordinated focus fire strategy"""
    actions = []
    agent_x, agent_y = agent['x'], agent['y']
    init_agent = get_agent_init_data(agent['agent_id'], agents_init)
    current_distance = manhattan_distance(agent_x, agent_y, closest_enemy['x'], closest_enemy['y'])

    # Try shooting from current position
    if agent['cooldown'] == 0 and current_distance <= 2 * init_agent['optimal_range']:
        effective_wetness, _, _, _ = calculate_effective_wetness(
            agent_x, agent_y, closest_enemy, grid, width, height,
            init_agent['soaking_power'], init_agent['optimal_range']
        )
        if effective_wetness > 0:
            actions.append(f"SHOOT {closest_enemy['agent_id']}")
            return actions

    # Try splash bomb from current position
    if agent['splash_bombs'] > 0 and current_distance <= MAX_THROW_RANGE:
        splash_targets = find_best_splash_targets(agent_x, agent_y, [closest_enemy], my_agents, width, height)
        if splash_targets:
            target_x, target_y = splash_targets[0]['target_pos']
            actions.append(f"THROW {target_x} {target_y}")
            return actions

    # Move to better position
    if agent['wetness'] >= 70:
        cover_pos, _ = find_closest_cover_position(agent, grid, width, height)
        if cover_pos and (agent_x, agent_y) != cover_pos:
            actions.append(f"MOVE {cover_pos[0]} {cover_pos[1]}")
    else:
        position_info = safe_positions.get(agent['agent_id'])
        if position_info:
            target_pos = position_info['pos']
            if (agent_x, agent_y) != target_pos:
                actions.append(f"MOVE {target_pos[0]} {target_pos[1]}")

    actions.append("HUNKER_DOWN")
    return actions

def find_optimal_throw_position(agent, enemy_agents, my_agents, grid, width, height):
    cells = detect_jail_cells(grid, width, height)
    agent_cell = find_agent_cell(agent, cells)

    if agent_cell:
        enemies_in_cell = [e for e in enemy_agents if (e['x'], e['y']) in agent_cell['interior']]
        if enemies_in_cell:
            safe_pos, throw_target = find_safe_throw_position_in_cell(enemies_in_cell, agent_cell)
            if safe_pos and throw_target:
                total_wetness = sum(e.get('wetness', 0) for e in enemies_in_cell)
                distance = manhattan_distance(safe_pos[0], safe_pos[1], throw_target[0], throw_target[1])
                targets = [create_target_info(throw_target, distance, enemies_in_cell, total_wetness)]
                return safe_pos, targets
    else:
        enemy_only_cells = find_cells_with_only_enemies(cells, enemy_agents, my_agents)
        if enemy_only_cells:
            best_cell_target = None
            best_throw_pos = None
            best_score = 0

            for cell_info in enemy_only_cells:
                cell = cell_info['cell']
                center_x, center_y = cell['center']

                reachable = find_reachable_tiles(agent['x'], agent['y'], grid,
                                               [a for a in my_agents if a['agent_id'] != agent['agent_id']],
                                               width, height, max_distance=3)

                for (pos_x, pos_y), _ in reachable.items():
                    distance = manhattan_distance(pos_x, pos_y, center_x, center_y)
                    if distance <= MAX_THROW_RANGE:
                        score = cell_info['enemy_count'] * 1000 - distance * 10
                        if score > best_score:
                            best_score = score
                            best_cell_target = cell_info
                            best_throw_pos = (pos_x, pos_y)

            if best_cell_target and best_throw_pos:
                cell = best_cell_target['cell']
                center_x, center_y = cell['center']
                distance = manhattan_distance(best_throw_pos[0], best_throw_pos[1], center_x, center_y)
                total_wetness = sum(e.get('wetness', 0) for e in best_cell_target['enemies'])
                targets = [create_target_info((center_x, center_y), distance, best_cell_target['enemies'], total_wetness)]
                return best_throw_pos, targets

    # Standard pathfinding
    other_agents = [a for a in my_agents if a['agent_id'] != agent['agent_id']]
    reachable = find_reachable_tiles(agent['x'], agent['y'], grid, other_agents, width, height, max_distance=3)

    best_position = None
    best_targets = []
    best_score = 0

    for (pos_x, pos_y), move_distance in reachable.items():
        targets = find_best_splash_targets(pos_x, pos_y, enemy_agents, my_agents, width, height)
        if targets:
            best_target = targets[0]
            score = (best_target['elimination_potential'] * 10000 +
                    best_target['enemy_count'] * 1000 +
                    best_target['total_damage'] - move_distance * 10)
            if score > best_score:
                best_score = score
                best_position = (pos_x, pos_y)
                best_targets = targets

    return best_position, best_targets

def execute_balanced_strategy(my_agents, enemy_agents, grid, width, height, agents_init):
    taken_enemies = set()
    agent_commands = {}

    for my_agent in sorted(my_agents, key=lambda x: x['agent_id']):
        actions = []
        agent_x, agent_y = my_agent['x'], my_agent['y']

        prioritize_cover = my_agent['wetness'] >= 30
        best_pos, _ = find_best_position(my_agent, grid, width, height, my_agents, enemy_agents, prioritize_cover)

        # High wetness - prioritize survival
        if my_agent['wetness'] >= 50:
            if best_pos and (agent_x, agent_y) != best_pos:
                actions.append(f"MOVE {best_pos[0]} {best_pos[1]}")
            actions.append("HUNKER_DOWN")

        # Try splash bombs first
        elif my_agent['splash_bombs'] > 0:
            success, splash_actions, enemies_hit = try_splash_attack(my_agent, enemy_agents, my_agents, width, height, taken_enemies)
            if success:
                actions.extend(splash_actions)
                taken_enemies.update(enemies_hit)
            else:
                # Move to better position or try shooting
                if best_pos and (agent_x, agent_y) != best_pos:
                    actions.append(f"MOVE {best_pos[0]} {best_pos[1]}")

                success, shoot_actions, enemies_hit = try_shooting_attack(my_agent, enemy_agents, grid, width, height, agents_init, taken_enemies)
                if success:
                    actions.extend(shoot_actions)
                    taken_enemies.update(enemies_hit)
                else:
                    actions.append("HUNKER_DOWN")

        # No splash bombs - focus on shooting
        else:
            success, shoot_actions, enemies_hit = try_shooting_attack(my_agent, enemy_agents, grid, width, height, agents_init, taken_enemies)
            if success:
                actions.extend(shoot_actions)
                taken_enemies.update(enemies_hit)
            else:
                # Move to better position
                if best_pos and (agent_x, agent_y) != best_pos:
                    actions.append(f"MOVE {best_pos[0]} {best_pos[1]}")
                actions.append("HUNKER_DOWN")

        status = f"W:{my_agent['wetness']} B:{my_agent['splash_bombs']} C:{my_agent['cooldown']}"
        agent_commands[my_agent['agent_id']] = build_agent_command(my_agent['agent_id'], actions, status)

    return agent_commands

def find_formation_positions(my_agents, target_enemy, grid, width, height):
    if not target_enemy:
        return {}

    target_x, target_y = target_enemy['x'], target_enemy['y']
    center_x = sum(agent['x'] for agent in my_agents) // len(my_agents)
    center_y = sum(agent['y'] for agent in my_agents) // len(my_agents)

    dx, dy = target_x - center_x, target_y - center_y

    if abs(dx) > abs(dy):
        line_direction = (0, 1) if dx > 0 else (0, -1)
        approach_direction = (1, 0) if dx > 0 else (-1, 0)
    else:
        line_direction = (1, 0) if dy > 0 else (-1, 0)
        approach_direction = (0, 1) if dy > 0 else (0, -1)

    base_x = target_x - approach_direction[0] * 4
    base_y = target_y - approach_direction[1] * 4

    formation_positions = {}
    start_offset = -(len(my_agents) - 1) // 2

    for i, agent in enumerate(sorted(my_agents, key=lambda x: x['agent_id'])):
        offset = start_offset + i
        pos_x = max(0, min(width - 1, base_x + line_direction[0] * offset * 2))
        pos_y = max(0, min(height - 1, base_y + line_direction[1] * offset * 2))

        # Find valid position if current is blocked
        tile = next((t for t in grid if t['x'] == pos_x and t['y'] == pos_y), None)
        if tile and tile['tile_type'] > 0:
            for dx in range(-2, 3):
                for dy in range(-2, 3):
                    alt_x, alt_y = pos_x + dx, pos_y + dy
                    if (0 <= alt_x < width and 0 <= alt_y < height and
                        not any(t['x'] == alt_x and t['y'] == alt_y and t['tile_type'] > 0 for t in grid)):
                        pos_x, pos_y = alt_x, alt_y
                        break
                else:
                    continue
                break

        formation_positions[agent['agent_id']] = (pos_x, pos_y)

    return formation_positions

def calculate_territory_split(my_agents, enemy_agents):
    my_center_x = sum(agent['x'] for agent in my_agents) / len(my_agents)
    my_center_y = sum(agent['y'] for agent in my_agents) / len(my_agents)
    enemy_center_x = sum(agent['x'] for agent in enemy_agents) / len(enemy_agents)
    enemy_center_y = sum(agent['y'] for agent in enemy_agents) / len(enemy_agents)
    mid_x = (my_center_x + enemy_center_x) / 2
    mid_y = (my_center_y + enemy_center_y) / 2
    return (my_center_x, my_center_y), (enemy_center_x, enemy_center_y), (mid_x, mid_y)

def find_safe_attack_positions(my_agents, target_enemy, enemy_agents, grid, width, height, agents_init):
    """Find positions where agents can attack target while staying safe from other enemies."""
    target_x, target_y = target_enemy['x'], target_enemy['y']
    safe_positions = {}

    # Get optimal ranges for our agents
    agent_ranges = {}
    for agent in my_agents:
        init_agent = next(init_a for init_a in agents_init if init_a['agent_id'] == agent['agent_id'])
        agent_ranges[agent['agent_id']] = init_agent['optimal_range']

    # Get enemy ranges for safety calculations
    enemy_ranges = {}
    for enemy in enemy_agents:
        init_enemy = next(init_a for init_a in agents_init if init_a['agent_id'] == enemy['agent_id'])
        enemy_ranges[enemy['agent_id']] = init_enemy['optimal_range']

    # For each agent, find positions that can attack target but stay safe from other enemies
    for agent in my_agents:
        agent_optimal_range = agent_ranges[agent['agent_id']]
        max_attack_range = 2 * agent_optimal_range  # Maximum effective range

        best_positions = []

        # Check positions in a circle around the target
        for dx in range(-max_attack_range, max_attack_range + 1):
            for dy in range(-max_attack_range, max_attack_range + 1):
                pos_x = target_x + dx
                pos_y = target_y + dy

                # Must be within map bounds
                if not (0 <= pos_x < width and 0 <= pos_y < height):
                    continue

                # Must be within attack range of target
                distance_to_target = manhattan_distance(pos_x, pos_y, target_x, target_y)
                if distance_to_target > max_attack_range:
                    continue

                # Must not be on cover
                tile = next((t for t in grid if t['x'] == pos_x and t['y'] == pos_y), None)
                if tile and tile['tile_type'] > 0:
                    continue

                # Calculate safety from other enemies
                min_enemy_distance = float('inf')
                for enemy in enemy_agents:
                    if enemy['agent_id'] == target_enemy['agent_id']:
                        continue  # Skip the target enemy

                    enemy_distance = manhattan_distance(pos_x, pos_y, enemy['x'], enemy['y'])
                    enemy_optimal_range = enemy_ranges.get(enemy['agent_id'], 3)  # Default to 3

                    # We want to stay outside enemy optimal range for safety
                    safety_distance = enemy_optimal_range + 1
                    if enemy_distance < safety_distance:
                        min_enemy_distance = -1  # Mark as unsafe
                        break
                    min_enemy_distance = min(min_enemy_distance, enemy_distance)

                if min_enemy_distance > 0:  # Position is safe
                    # Check for nearby cover
                    cover_bonus = 0
                    adjacent_tiles = get_adjacent_tiles(pos_x, pos_y, width, height)
                    for adj_x, adj_y in adjacent_tiles:
                        adj_tile = next((t for t in grid if t['x'] == adj_x and t['y'] == adj_y), None)
                        if adj_tile and adj_tile['tile_type'] > 0:
                            cover_bonus = adj_tile['tile_type'] * 10
                            break

                    # Score position: prefer positions where we can deal ANY damage
                    if distance_to_target <= agent_optimal_range:
                        range_score = 100  # Optimal range - maximum damage
                    elif distance_to_target <= 2 * agent_optimal_range:
                        range_score = 80   # Extended range - still effective
                    else:
                        range_score = 20   # Long range - minimal damage but still something

                    safety_score = min(min_enemy_distance, 10) * 5  # Cap safety bonus
                    total_score = range_score + cover_bonus + safety_score

                    best_positions.append({
                        'pos': (pos_x, pos_y),
                        'score': total_score,
                        'distance_to_target': distance_to_target,
                        'cover_bonus': cover_bonus,
                        'safety_distance': min_enemy_distance
                    })

        # Sort by score and store all good positions
        if best_positions:
            best_positions.sort(key=lambda x: -x['score'])
            safe_positions[agent['agent_id']] = best_positions  # Store all options

        else:
            # No safe position found, find ANY position where we can shoot (even if not safe)
            fallback_positions = []
            for dx in range(-max_attack_range, max_attack_range + 1):
                for dy in range(-max_attack_range, max_attack_range + 1):
                    pos_x = target_x + dx
                    pos_y = target_y + dy

                    if not (0 <= pos_x < width and 0 <= pos_y < height):
                        continue

                    distance_to_target = manhattan_distance(pos_x, pos_y, target_x, target_y)
                    if distance_to_target > max_attack_range:
                        continue

                    # Must not be on cover
                    tile = next((t for t in grid if t['x'] == pos_x and t['y'] == pos_y), None)
                    if tile and tile['tile_type'] > 0:
                        continue

                    # Any position where we can deal damage
                    fallback_positions.append({
                        'pos': (pos_x, pos_y),
                        'score': 10,  # Low score but better than nothing
                        'distance_to_target': distance_to_target,
                        'cover_bonus': 0,
                        'safety_distance': 0
                    })

            if fallback_positions:
                # Sort by distance to target (prefer closer)
                fallback_positions.sort(key=lambda x: x['distance_to_target'])
                safe_positions[agent['agent_id']] = fallback_positions

            else:
                # Absolutely no position found, use current position
                safe_positions[agent['agent_id']] = [{
                    'pos': (agent['x'], agent['y']),
                    'score': 0,
                    'distance_to_target': manhattan_distance(agent['x'], agent['y'], target_x, target_y),
                    'cover_bonus': 0,
                    'safety_distance': 0
                }]


    # Resolve position conflicts - ensure agents don't try to occupy same tile
    final_positions = {}
    used_positions = set()

    # Sort agents by priority (could be by agent ID, distance to target, etc.)
    agent_priority = sorted(my_agents, key=lambda x: x['agent_id'])

    for agent in agent_priority:
        agent_options = safe_positions[agent['agent_id']]

        # Find the best available position
        chosen_position = None
        for option in agent_options:
            if option['pos'] not in used_positions:
                chosen_position = option
                break

        if chosen_position:
            final_positions[agent['agent_id']] = chosen_position
            used_positions.add(chosen_position['pos'])
        else:
            # All preferred positions taken, use current position
            final_positions[agent['agent_id']] = {
                'pos': (agent['x'], agent['y']),
                'score': 0,
                'distance_to_target': manhattan_distance(agent['x'], agent['y'], target_x, target_y),
                'cover_bonus': 0,
                'safety_distance': 0
            }

    return final_positions

def execute_coordinated_focus_fire_strategy(my_agents, enemy_agents, grid, width, height, agents_init):
    if not enemy_agents:
        return {agent['agent_id']: build_agent_command(agent['agent_id'], ["HUNKER_DOWN"], "No enemies")
                for agent in my_agents}

    my_center, _, _ = calculate_territory_split(my_agents, enemy_agents)
    closest_enemy = min(enemy_agents, key=lambda e: manhattan_distance(my_center[0], my_center[1], e['x'], e['y']))
    safe_positions = find_safe_attack_positions(my_agents, closest_enemy, enemy_agents, grid, width, height, agents_init)

    agent_commands = {}
    for agent in sorted(my_agents, key=lambda x: x['agent_id']):
        actions = process_focus_fire_agent(agent, closest_enemy, safe_positions, grid, width, height, agents_init, my_agents)
        status = f"FF W:{agent['wetness']} D:{manhattan_distance(agent['x'], agent['y'], closest_enemy['x'], closest_enemy['y'])}"
        agent_commands[agent['agent_id']] = build_agent_command(agent['agent_id'], actions, status)

    return agent_commands

def calculate_front_line_control(my_agents, enemy_agents, grid, width, height):
    """Calculate predictive front-line territory control and identify strategic positions."""
    # Find the center line between our forces and enemy forces
    my_center_x = sum(agent['x'] for agent in my_agents) / len(my_agents)
    my_center_y = sum(agent['y'] for agent in my_agents) / len(my_agents)
    enemy_center_x = sum(agent['x'] for agent in enemy_agents) / len(enemy_agents)
    enemy_center_y = sum(agent['y'] for agent in enemy_agents) / len(enemy_agents)

    # Determine which side of the map we should control (our side + some forward territory)
    # We want to control slightly more than half the map, biased toward our side
    our_side_bias = 0.6  # Control 60% of the distance toward enemy
    target_line_x = my_center_x + (enemy_center_x - my_center_x) * our_side_bias
    target_line_y = my_center_y + (enemy_center_y - my_center_y) * our_side_bias

    # Find strategic front-line positions with cover
    front_line_positions = []
    target_positions = {}  # Positions we should control

    # Scan the map for good defensive positions near our target line
    for x in range(width):
        for y in range(height):
            # Check if this position is near our target control line
            distance_to_target_line = abs((x - target_line_x) + (y - target_line_y)) / 2

            if distance_to_target_line <= 3:  # Within 3 tiles of our target line
                tile = next((t for t in grid if t['x'] == x and t['y'] == y), None)
                if tile and tile['tile_type'] == 0:  # Not on cover, but check for adjacent cover

                    # Check for adjacent cover
                    cover_bonus = 0
                    adjacent_tiles = get_adjacent_tiles(x, y, width, height)
                    for adj_x, adj_y in adjacent_tiles:
                        adj_tile = next((t for t in grid if t['x'] == adj_x and t['y'] == adj_y), None)
                        if adj_tile and adj_tile['tile_type'] > 0:
                            cover_bonus = max(cover_bonus, adj_tile['tile_type'])

                    # Calculate strategic value
                    # Distance from our center (closer to us is better for fallback)
                    distance_from_us = manhattan_distance(x, y, my_center_x, my_center_y)
                    # Distance from enemy center (closer to them gives more control)
                    distance_from_enemy = manhattan_distance(x, y, enemy_center_x, enemy_center_y)

                    strategic_value = cover_bonus * 10 + (20 - distance_from_us) + (10 - distance_from_enemy)

                    if strategic_value > 15:  # Only consider good positions
                        front_line_positions.append({
                            'pos': (x, y),
                            'cover_bonus': cover_bonus,
                            'strategic_value': strategic_value,
                            'distance_from_us': distance_from_us,
                            'distance_from_enemy': distance_from_enemy
                        })

    # Sort front-line positions by strategic value
    front_line_positions.sort(key=lambda x: -x['strategic_value'])

    # Calculate current control based on our target front line
    my_controlled = 0
    enemy_controlled = 0
    contested = 0

    for x in range(width):
        for y in range(height):
            # Find closest agent from each team
            min_my_distance = float('inf')
            min_enemy_distance = float('inf')

            for agent in my_agents:
                distance = manhattan_distance(x, y, agent['x'], agent['y'])
                min_my_distance = min(min_my_distance, distance)

            for agent in enemy_agents:
                distance = manhattan_distance(x, y, agent['x'], agent['y'])
                min_enemy_distance = min(min_enemy_distance, distance)

            # Determine control with bias toward strategic positions
            is_strategic = any(pos['pos'] == (x, y) for pos in front_line_positions[:10])  # Top 10 strategic positions

            if min_my_distance < min_enemy_distance:
                my_controlled += 1
                if is_strategic:
                    my_controlled += 1  # Bonus for controlling strategic positions
            elif min_enemy_distance < min_my_distance:
                enemy_controlled += 1
                if is_strategic:
                    enemy_controlled += 1  # Penalty for enemy controlling strategic positions
            else:
                contested += 1

    # Assign target positions for our agents
    for i, agent in enumerate(my_agents):
        if i < len(front_line_positions):
            target_positions[agent['agent_id']] = front_line_positions[i]['pos']
        else:
            # Fallback to current position if no strategic position available
            target_positions[agent['agent_id']] = (agent['x'], agent['y'])

    advantage = my_controlled - enemy_controlled

    return {
        'my_control': my_controlled,
        'enemy_control': enemy_controlled,
        'contested': contested,
        'advantage': advantage,
        'front_line': front_line_positions[:5],  # Top 5 strategic positions
        'target_positions': target_positions,
        'target_line': (target_line_x, target_line_y)
    }

def assess_agent_rotation_needs(my_agents, enemy_agents, agents_init, front_line_positions):
    """Assess which agents need to rotate based on health and threat level."""
    rotation_plan = {
        'rotate_out': [],  # Agents that should move to safety
        'rotate_in': [],   # Agents that should move to front line
        'hold_position': []  # Agents that should stay where they are
    }

    # Calculate threat level for each agent
    for agent in my_agents:
        agent_x, agent_y = agent['x'], agent['y']
        wetness = agent.get('wetness', 0)

        # Calculate incoming threat
        potential_damage = 0
        splash_threat = False

        for enemy in enemy_agents:
            distance = manhattan_distance(agent_x, agent_y, enemy['x'], enemy['y'])

            # Check shooting threat
            enemy_init = next(init_a for init_a in agents_init if init_a['agent_id'] == enemy['agent_id'])
            if distance <= 2 * enemy_init['optimal_range']:
                if distance <= enemy_init['optimal_range']:
                    potential_damage += enemy_init['soaking_power']  # Full damage
                else:
                    potential_damage += enemy_init['soaking_power'] * 0.5  # Half damage

            # Check splash bomb threat
            if enemy.get('splash_bombs', 0) > 0 and distance <= 4:
                splash_threat = True
                potential_damage += 30  # Splash bomb damage

        # Determine if agent is in critical danger
        health_after_damage = wetness + potential_damage
        is_critical = health_after_damage >= 80  # Will die in 1-2 turns
        is_moderate_risk = health_after_damage >= 60  # Significant risk

        # Check if agent is on front line
        is_on_front_line = any(
            manhattan_distance(agent_x, agent_y, pos['pos'][0], pos['pos'][1]) <= 2
            for pos in front_line_positions
        )

        # Make rotation decision
        if is_critical and is_on_front_line:
            rotation_plan['rotate_out'].append({
                'agent_id': agent['agent_id'],
                'reason': f'Critical health risk (wetness: {wetness}, potential damage: {potential_damage})',
                'priority': 'HIGH'
            })
        elif is_moderate_risk and is_on_front_line and splash_threat:
            rotation_plan['rotate_out'].append({
                'agent_id': agent['agent_id'],
                'reason': f'Moderate risk with splash threat (wetness: {wetness})',
                'priority': 'MEDIUM'
            })
        elif wetness < 30 and not is_on_front_line:
            rotation_plan['rotate_in'].append({
                'agent_id': agent['agent_id'],
                'reason': f'Healthy agent available for front line (wetness: {wetness})',
                'priority': 'LOW'
            })
        else:
            rotation_plan['hold_position'].append({
                'agent_id': agent['agent_id'],
                'reason': f'Stable position (wetness: {wetness}, front_line: {is_on_front_line})'
            })

    return rotation_plan

def detect_move_collisions(planned_moves, all_agents, grid, width, height):
    """Detect and resolve move collisions based on server collision logic.

    Server behavior:
    1. Process agents sequentially for pathfinding (earlier agents have priority)
    2. Only check start/end positions for moving agent collisions (NOT full paths)
    3. Visual path crossings are allowed if start/end don't conflict
    """
    # planned_moves is a dict: {agent_id: {'from': (x, y), 'to': (x, y)}}

    collisions = []
    move_list = list(planned_moves.items())

    # Simulate server's sequential processing for pathfinding
    # Create initial static positions (all non-moving agents)
    moving_agent_ids = set(planned_moves.keys())
    static_positions = set()
    for agent in all_agents:
        if agent['agent_id'] not in moving_agent_ids:
            static_positions.add((agent['x'], agent['y']))

    # Process each moving agent sequentially (like server does)
    remaining_static = static_positions.copy()
    for agent in all_agents:
        if agent['agent_id'] in planned_moves:
            agent_id = agent['agent_id']
            move = planned_moves[agent_id]
            from_pos = move['from']
            to_pos = move['to']

            # Check if path is blocked by remaining static agents
            if not has_valid_path(from_pos, to_pos, remaining_static, grid, width, height):
                collisions.append({
                    'type': 'path_collision',
                    'agents': [agent_id],
                    'reason': f"Path from {from_pos} to {to_pos} blocked by static agent"
                })

            # Remove this agent from static positions (server does this at line 477)
            current_pos = (agent['x'], agent['y'])
            if current_pos in remaining_static:
                remaining_static.remove(current_pos)

    # Check for target and swap collisions between moving agents (server lines 503-510)
    # NOTE: Server does NOT check for path crossings, only start/end positions
    for i, (agent_id1, move1) in enumerate(move_list):
        for j, (agent_id2, move2) in enumerate(move_list):
            if i >= j:  # Avoid checking same pair twice
                continue

            # Check for target collision (both moving to same destination)
            if move1['to'] == move2['to']:
                collisions.append({
                    'type': 'target_collision',
                    'agents': [agent_id1, agent_id2],
                    'reason': f"Both agents moving to {move1['to']}"
                })

            # Check for swap collision (agents swapping positions)
            elif move1['to'] == move2['from'] and move1['from'] == move2['to']:
                collisions.append({
                    'type': 'swap_collision',
                    'agents': [agent_id1, agent_id2],
                    'reason': f"Agents swapping positions {move1['from']} <-> {move1['to']}"
                })

    return collisions

def has_valid_path(from_pos, to_pos, blocked_positions, grid, width, height):
    """Check if there's a valid path from from_pos to to_pos avoiding blocked positions.

    This mimics the server's behavior: the server uses A* pathfinding with restricted
    coordinates, and if no path exists, the move is cancelled.
    """
    if from_pos == to_pos:
        return True

    # Use BFS to find if any path exists (similar to server's A*)
    from collections import deque

    queue = deque([from_pos])
    visited = {from_pos}

    while queue:
        current = queue.popleft()

        # Check all adjacent positions
        for next_pos in get_adjacent_tiles(current[0], current[1], width, height):
            if next_pos in visited:
                continue

            # Check if position is blocked by static agent
            if next_pos in blocked_positions:
                continue

            # Check if position has cover
            if not can_move_to_tile(next_pos[0], next_pos[1], grid, []):
                continue

            # Found target
            if next_pos == to_pos:
                return True

            visited.add(next_pos)
            queue.append(next_pos)

    return False

def resolve_move_collisions(my_agents, planned_moves, grid, width, height):
    """Resolve move collisions by prioritizing agents and finding alternative moves."""
    collisions = detect_move_collisions(planned_moves, my_agents, grid, width, height)

    if not collisions:
        return planned_moves, []

    # Priority system: prioritize agents with higher health and better strategic position
    agent_priorities = {}
    for agent in my_agents:
        # Higher priority = lower wetness + higher agent_id (for consistency)
        priority = (100 - agent.get('wetness', 0)) * 10 + agent['agent_id']
        agent_priorities[agent['agent_id']] = priority

    resolved_moves = planned_moves.copy()
    cancelled_moves = []

    for collision in collisions:
        agents_in_collision = collision['agents']

        # For path collisions, there's only one agent - just cancel the move
        if collision['type'] == 'path_collision':
            agent_id = agents_in_collision[0]
            if agent_id in resolved_moves:
                cancelled_move = resolved_moves[agent_id]
                del resolved_moves[agent_id]
                cancelled_moves.append({
                    'agent_id': agent_id,
                    'cancelled_move': cancelled_move,
                    'reason': collision['reason'],
                    'winner': None  # No winner for path collisions
                })
        else:
            # For target/swap collisions, prioritize by agent priority
            agents_in_collision.sort(key=lambda aid: agent_priorities.get(aid, 0), reverse=True)

            # Keep the highest priority agent's move, cancel others
            winner = agents_in_collision[0]
            losers = agents_in_collision[1:]

            for loser_id in losers:
                if loser_id in resolved_moves:
                    cancelled_move = resolved_moves[loser_id]
                    del resolved_moves[loser_id]
                    cancelled_moves.append({
                        'agent_id': loser_id,
                        'cancelled_move': cancelled_move,
                        'reason': collision['reason'],
                        'winner': winner
                    })

    return resolved_moves, cancelled_moves

def find_alternative_move(agent, original_target, grid, width, height, planned_moves):
    """Find an alternative move for an agent whose original move was cancelled."""
    agent_x, agent_y = agent['x'], agent['y']

    # Get all positions that other agents are moving to
    occupied_targets = set(move['to'] for move in planned_moves.values())
    occupied_targets.add((agent_x, agent_y))  # Current position is also occupied

    # Find alternative positions near the original target
    alternatives = []
    target_x, target_y = original_target

    # Search in expanding radius around original target
    for radius in range(1, 4):
        for dx in range(-radius, radius + 1):
            for dy in range(-radius, radius + 1):
                if abs(dx) != radius and abs(dy) != radius:
                    continue  # Only check perimeter of current radius

                alt_x = target_x + dx
                alt_y = target_y + dy

                # Must be within map bounds
                if not (0 <= alt_x < width and 0 <= alt_y < height):
                    continue

                # Must not be on cover
                tile = next((t for t in grid if t['x'] == alt_x and t['y'] == alt_y), None)
                if tile and tile['tile_type'] > 0:
                    continue

                # Must not be occupied by other planned moves
                if (alt_x, alt_y) in occupied_targets:
                    continue

                # Must be reachable in one move
                move_distance = manhattan_distance(agent_x, agent_y, alt_x, alt_y)
                if move_distance != 1:
                    continue

                # Calculate value of this alternative
                distance_to_original = manhattan_distance(alt_x, alt_y, target_x, target_y)

                # Prefer positions closer to original target
                score = 10 - distance_to_original

                # Bonus for cover adjacency
                adjacent_tiles = get_adjacent_tiles(alt_x, alt_y, width, height)
                for adj_x, adj_y in adjacent_tiles:
                    adj_tile = next((t for t in grid if t['x'] == adj_x and t['y'] == adj_y), None)
                    if adj_tile and adj_tile['tile_type'] > 0:
                        score += adj_tile['tile_type'] * 2

                alternatives.append({
                    'pos': (alt_x, alt_y),
                    'score': score,
                    'distance_to_original': distance_to_original
                })

    if alternatives:
        # Sort by score and return best alternative
        alternatives.sort(key=lambda x: -x['score'])
        best_alt = alternatives[0]

        return best_alt['pos']

    # No alternative found, stay put

    return (agent_x, agent_y)

def calculate_map_control(my_agents, enemy_agents, width, height):
    """Calculate how much of the map each team controls using Voronoi-like analysis."""
    my_controlled = 0
    enemy_controlled = 0
    contested = 0

    for x in range(width):
        for y in range(height):
            # Find closest agent from each team
            min_my_distance = float('inf')
            min_enemy_distance = float('inf')

            for agent in my_agents:
                distance = manhattan_distance(x, y, agent['x'], agent['y'])
                min_my_distance = min(min_my_distance, distance)

            for agent in enemy_agents:
                distance = manhattan_distance(x, y, agent['x'], agent['y'])
                min_enemy_distance = min(min_enemy_distance, distance)

            # Determine control
            if min_my_distance < min_enemy_distance:
                my_controlled += 1
            elif min_enemy_distance < min_my_distance:
                enemy_controlled += 1
            else:
                contested += 1

    control_advantage = my_controlled - enemy_controlled
    return my_controlled, enemy_controlled, contested, control_advantage

def find_defensive_positions(my_agents, enemy_agents, grid, width, height, agents_init):
    """Find defensive positions that maintain spacing and cover while controlling territory."""
    positions = {}

    # Calculate current map control
    _, _, _, advantage = calculate_map_control(my_agents, enemy_agents, width, height)


    # Determine if we need to be aggressive or can stay defensive
    need_aggression = advantage < 3  # Push forward if we don't have 3+ tile advantage

    for agent in my_agents:
        agent_x, agent_y = agent['x'], agent['y']
        init_agent = next(init_a for init_a in agents_init if init_a['agent_id'] == agent['agent_id'])
        max_range = 2 * init_agent['optimal_range']

        best_positions = []

        # Search area around current position
        search_radius = 3 if need_aggression else 2
        for dx in range(-search_radius, search_radius + 1):
            for dy in range(-search_radius, search_radius + 1):
                pos_x = agent_x + dx
                pos_y = agent_y + dy

                # Must be within map bounds
                if not (0 <= pos_x < width and 0 <= pos_y < height):
                    continue

                # Must not be on cover
                tile = next((t for t in grid if t['x'] == pos_x and t['y'] == pos_y), None)
                if tile and tile['tile_type'] > 0:
                    continue

                # Check spacing from other friendly agents (must be 3+ tiles apart)
                # Also check against other agents' planned positions
                too_close = False
                for other_agent in my_agents:
                    if other_agent['agent_id'] == agent['agent_id']:
                        continue

                    # Check distance from other agent's current position
                    distance = manhattan_distance(pos_x, pos_y, other_agent['x'], other_agent['y'])
                    if distance < 3:
                        too_close = True
                        break

                    # Check distance from other agent's planned position (if already calculated)
                    if other_agent['agent_id'] in positions:
                        other_planned_pos = positions[other_agent['agent_id']]['pos']
                        planned_distance = manhattan_distance(pos_x, pos_y, other_planned_pos[0], other_planned_pos[1])
                        if planned_distance < 3:
                            too_close = True
                            break

                if too_close:
                    continue

                # Calculate position value
                score = 0

                # Cover bonus
                cover_bonus = 0
                adjacent_tiles = get_adjacent_tiles(pos_x, pos_y, width, height)
                for adj_x, adj_y in adjacent_tiles:
                    adj_tile = next((t for t in grid if t['x'] == adj_x and t['y'] == adj_y), None)
                    if adj_tile and adj_tile['tile_type'] > 0:
                        cover_bonus = max(cover_bonus, adj_tile['tile_type'] * 20)

                # Territory control bonus
                territory_bonus = 0
                for enemy in enemy_agents:
                    enemy_distance = manhattan_distance(pos_x, pos_y, enemy['x'], enemy['y'])
                    if enemy_distance <= max_range:
                        territory_bonus += 15  # Can threaten enemy

                # Central position bonus (prefer controlling center)
                center_x, center_y = width // 2, height // 2
                center_distance = manhattan_distance(pos_x, pos_y, center_x, center_y)
                center_bonus = max(0, 10 - center_distance)

                # Safety from enemy splash bombs
                safety_bonus = 0
                min_enemy_distance = float('inf')
                for enemy in enemy_agents:
                    enemy_distance = manhattan_distance(pos_x, pos_y, enemy['x'], enemy['y'])
                    min_enemy_distance = min(min_enemy_distance, enemy_distance)

                if min_enemy_distance > 4:  # Outside splash bomb range
                    safety_bonus = 15
                elif min_enemy_distance > 3:  # Marginal safety
                    safety_bonus = 5

                score = cover_bonus + territory_bonus + center_bonus + safety_bonus

                best_positions.append({
                    'pos': (pos_x, pos_y),
                    'score': score,
                    'cover_bonus': cover_bonus,
                    'territory_bonus': territory_bonus,
                    'safety_bonus': safety_bonus
                })

        # Sort by score and pick best
        if best_positions:
            best_positions.sort(key=lambda x: -x['score'])
            positions[agent['agent_id']] = best_positions[0]

        else:
            # No good position found, stay put
            positions[agent['agent_id']] = {
                'pos': (agent_x, agent_y),
                'score': 0,
                'cover_bonus': 0,
                'territory_bonus': 0,
                'safety_bonus': 0
            }

    return positions, need_aggression

def find_best_target_for_defensive_strategy(my_agents, enemy_agents, agents_init):
    """Find the best target for defensive strategy - prioritize weak/exposed enemies."""
    if not enemy_agents:
        return None

    target_scores = []

    for enemy in enemy_agents:
        # Prioritize weak enemies (high wetness)
        weakness_score = enemy.get('wetness', 0)

        # Prioritize exposed enemies (far from cover)
        # This is simplified - in a real implementation we'd check actual cover positions
        exposure_score = 10  # Base exposure

        # Prioritize enemies we can actually hit
        can_hit_score = 0
        for agent in my_agents:
            init_agent = next(init_a for init_a in agents_init if init_a['agent_id'] == agent['agent_id'])
            distance = manhattan_distance(agent['x'], agent['y'], enemy['x'], enemy['y'])
            if distance <= 2 * init_agent['optimal_range']:
                can_hit_score += 20

        total_score = weakness_score + exposure_score + can_hit_score
        target_scores.append((enemy, total_score))

    # Sort by score and return best target
    target_scores.sort(key=lambda x: -x[1])
    best_target = target_scores[0][0]


    return best_target

def execute_defensive_control_strategy(my_agents, enemy_agents, grid, width, height, agents_init):
    """Execute defensive control strategy - spacing, cover, map control, selective engagement."""
    agent_commands = {}

    print(f"DEBUG: Defensive control strategy with {len(my_agents)} agents", file=sys.stderr)

    if not enemy_agents:
        # No enemies, just hunker down
        for agent in my_agents:
            command = f"{agent['agent_id']};HUNKER_DOWN;MESSAGE No enemies"
            agent_commands[agent['agent_id']] = command
        return agent_commands

    # Find defensive positions and determine aggression level
    defensive_positions, need_aggression = find_defensive_positions(my_agents, enemy_agents, grid, width, height, agents_init)

    # Find best target for coordinated attacks
    target_enemy = find_best_target_for_defensive_strategy(my_agents, enemy_agents, agents_init)

    print(f"DEBUG: Aggression needed: {need_aggression}, Target: Enemy {target_enemy['agent_id'] if target_enemy else 'None'}", file=sys.stderr)

    # Process each agent
    for agent in sorted(my_agents, key=lambda x: x['agent_id']):
        command = f"{agent['agent_id']}"
        agent_x, agent_y = agent['x'], agent['y']
        init_agent = next(init_a for init_a in agents_init if init_a['agent_id'] == agent['agent_id'])

        # Get defensive position
        position_info = defensive_positions.get(agent['agent_id'])
        target_pos = position_info['pos'] if position_info else (agent_x, agent_y)

        print(f"DEBUG: Agent {agent['agent_id']} at ({agent_x}, {agent_y}), defensive pos: {target_pos}", file=sys.stderr)

        # Check if agent is critically damaged
        if agent['wetness'] >= 60:
            # High priority on survival
            if (agent_x, agent_y) != target_pos:
                move_x, move_y = target_pos
                command += f";MOVE {move_x} {move_y}"
                print(f"DEBUG: Agent {agent['agent_id']} critical health, moving to safety ({move_x}, {move_y})", file=sys.stderr)
            command += ";HUNKER_DOWN"

        # Check if we can attack target from current position
        elif target_enemy and agent['cooldown'] == 0:
            distance_to_target = manhattan_distance(agent_x, agent_y, target_enemy['x'], target_enemy['y'])

            if distance_to_target <= 2 * init_agent['optimal_range']:
                effective_wetness, _, _, _ = calculate_effective_wetness(
                    agent_x, agent_y, target_enemy, grid, width, height,
                    init_agent['soaking_power'], init_agent['optimal_range']
                )

                if effective_wetness > 0:
                    command += f";SHOOT {target_enemy['agent_id']}"
                    print(f"DEBUG: Agent {agent['agent_id']} shooting target from current position, effective wetness: {effective_wetness}", file=sys.stderr)
                else:
                    # Move to defensive position if can't shoot effectively
                    if (agent_x, agent_y) != target_pos:
                        move_x, move_y = target_pos
                        command += f";MOVE {move_x} {move_y}"
                        print(f"DEBUG: Agent {agent['agent_id']} moving to defensive position ({move_x}, {move_y})", file=sys.stderr)
                    command += ";HUNKER_DOWN"
            else:
                # Target too far, move to defensive position
                if (agent_x, agent_y) != target_pos:
                    move_x, move_y = target_pos
                    command += f";MOVE {move_x} {move_y}"

                    # Check if we can shoot from new position
                    new_distance = manhattan_distance(move_x, move_y, target_enemy['x'], target_enemy['y'])
                    if new_distance <= 2 * init_agent['optimal_range']:
                        effective_wetness, _, _, _ = calculate_effective_wetness(
                            move_x, move_y, target_enemy, grid, width, height,
                            init_agent['soaking_power'], init_agent['optimal_range']
                        )

                        if effective_wetness > 0:
                            command += f";SHOOT {target_enemy['agent_id']}"
                            print(f"DEBUG: Agent {agent['agent_id']} moved and shooting, effective wetness: {effective_wetness}", file=sys.stderr)
                        else:
                            command += ";HUNKER_DOWN"
                    else:
                        command += ";HUNKER_DOWN"
                else:
                    command += ";HUNKER_DOWN"

        # Can't attack, focus on positioning
        else:
            if (agent_x, agent_y) != target_pos:
                move_x, move_y = target_pos
                command += f";MOVE {move_x} {move_y}"
                print(f"DEBUG: Agent {agent['agent_id']} moving to defensive position ({move_x}, {move_y})", file=sys.stderr)
            command += ";HUNKER_DOWN"

        # Add status message
        status = f"DC W:{agent['wetness']} A:{'+' if need_aggression else '-'}"
        command += f";MESSAGE {status}"

        agent_commands[agent['agent_id']] = command

    return agent_commands

def calculate_splash_threat_zones(enemy_agents, width, height):
    """Calculate all possible splash bomb threat zones from enemies with splash bombs."""
    threat_zones = {}  # Maps (x, y) -> list of enemies that can splash there

    for enemy in enemy_agents:
        if enemy.get('splash_bombs', 0) <= 0:
            continue

        enemy_threats = []

        # For each possible throw target within range 4
        for target_x in range(max(0, enemy['x'] - 4), min(width, enemy['x'] + 5)):
            for target_y in range(max(0, enemy['y'] - 4), min(height, enemy['y'] + 5)):
                throw_distance = manhattan_distance(enemy['x'], enemy['y'], target_x, target_y)
                if throw_distance > 4:
                    continue

                # Get splash area for this throw
                splash_area = get_splash_area(target_x, target_y, width, height)

                # Record this threat
                threat_info = {
                    'enemy': enemy,
                    'throw_target': (target_x, target_y),
                    'splash_area': splash_area
                }
                enemy_threats.append(threat_info)

                # Add each splash tile to threat zones
                for splash_x, splash_y in splash_area:
                    if (splash_x, splash_y) not in threat_zones:
                        threat_zones[(splash_x, splash_y)] = []
                    threat_zones[(splash_x, splash_y)].append(threat_info)

        print(f"DEBUG: Enemy {enemy['agent_id']} can create {len(enemy_threats)} splash threats", file=sys.stderr)

    return threat_zones

def check_position_safety(positions, threat_zones):
    """Check if proposed agent positions would allow multiple agents to be hit by same splash bomb."""
    violations = []

    # Group positions by the threats that could hit them
    threat_to_agents = {}

    for agent_id, pos in positions.items():
        pos_x, pos_y = pos

        # Check what threats could hit this position
        if (pos_x, pos_y) in threat_zones:
            for threat_info in threat_zones[(pos_x, pos_y)]:
                threat_key = (threat_info['enemy']['agent_id'], threat_info['throw_target'])

                if threat_key not in threat_to_agents:
                    threat_to_agents[threat_key] = []
                threat_to_agents[threat_key].append(agent_id)

    # Find violations (threats that could hit multiple agents)
    for threat_key, agents_at_risk in threat_to_agents.items():
        if len(agents_at_risk) > 1:
            enemy_id, throw_target = threat_key
            violations.append({
                'enemy_id': enemy_id,
                'throw_target': throw_target,
                'agents_at_risk': agents_at_risk,
                'risk_count': len(agents_at_risk)
            })

    return violations

def find_safe_positions_avoiding_splash(my_agents, target_enemy, enemy_agents, grid, width, height, agents_init, force_aggression=False, target_positions=None, rotation_plan=None):
    """Find positions where agents can attack while ensuring no splash bomb can hit multiple agents and considering front-line control."""
    threat_zones = calculate_splash_threat_zones(enemy_agents, width, height)

    aggression_note = " (FORCED AGGRESSION - prioritizing territory over safety)" if force_aggression else ""
    front_line_note = " with front-line bias" if target_positions else ""
    print(f"DEBUG: Calculated {len(threat_zones)} threat zone tiles{aggression_note}{front_line_note}", file=sys.stderr)

    # Find candidate positions for each agent
    agent_candidates = {}

    for agent in my_agents:
        agent_x, agent_y = agent['x'], agent['y']
        init_agent = next(init_a for init_a in agents_init if init_a['agent_id'] == agent['agent_id'])
        max_range = 2 * init_agent['optimal_range']

        candidates = []

        # Search for positions where we can shoot the target
        search_radius = min(max_range, 6)  # Limit search to reasonable area
        for dx in range(-search_radius, search_radius + 1):
            for dy in range(-search_radius, search_radius + 1):
                pos_x = agent_x + dx
                pos_y = agent_y + dy

                # Must be within map bounds
                if not (0 <= pos_x < width and 0 <= pos_y < height):
                    continue

                # Must not be on cover
                tile = next((t for t in grid if t['x'] == pos_x and t['y'] == pos_y), None)
                if tile and tile['tile_type'] > 0:
                    continue

                # Must be able to shoot target
                distance_to_target = manhattan_distance(pos_x, pos_y, target_enemy['x'], target_enemy['y'])
                if distance_to_target > max_range:
                    continue

                # Calculate position score
                # Range score
                if distance_to_target <= init_agent['optimal_range']:
                    range_score = 100
                else:
                    range_score = 50

                # Cover bonus
                cover_bonus = 0
                adjacent_tiles = get_adjacent_tiles(pos_x, pos_y, width, height)
                for adj_x, adj_y in adjacent_tiles:
                    adj_tile = next((t for t in grid if t['x'] == adj_x and t['y'] == adj_y), None)
                    if adj_tile and adj_tile['tile_type'] > 0:
                        cover_bonus = max(cover_bonus, adj_tile['tile_type'] * 15)

                # Distance from enemies (prefer further away)
                min_enemy_distance = float('inf')
                for enemy in enemy_agents:
                    enemy_distance = manhattan_distance(pos_x, pos_y, enemy['x'], enemy['y'])
                    min_enemy_distance = min(min_enemy_distance, enemy_distance)

                distance_safety_score = min(min_enemy_distance, 8) * 2

                # Front-line control bonus
                front_line_bonus = 0
                if target_positions and agent['agent_id'] in target_positions:
                    target_pos = target_positions[agent['agent_id']]
                    distance_to_target_pos = manhattan_distance(pos_x, pos_y, target_pos[0], target_pos[1])
                    if distance_to_target_pos <= 2:
                        front_line_bonus = 30  # Strong bonus for being near assigned front-line position
                    elif distance_to_target_pos <= 4:
                        front_line_bonus = 15  # Moderate bonus for being reasonably close

                # Rotation bonus/penalty
                rotation_bonus = 0
                if rotation_plan:
                    # Check if this agent should rotate out (penalty for staying forward)
                    should_rotate_out = any(r['agent_id'] == agent['agent_id'] for r in rotation_plan.get('rotate_out', []))
                    should_rotate_in = any(r['agent_id'] == agent['agent_id'] for r in rotation_plan.get('rotate_in', []))

                    if should_rotate_out:
                        # Penalty for staying in dangerous forward positions
                        if min_enemy_distance <= 6:
                            rotation_bonus = -20
                    elif should_rotate_in:
                        # Bonus for moving to front-line positions
                        if min_enemy_distance <= 8:
                            rotation_bonus = 15

                total_score = range_score + cover_bonus + distance_safety_score + front_line_bonus + rotation_bonus

                candidates.append({
                    'pos': (pos_x, pos_y),
                    'score': total_score,
                    'distance_to_target': distance_to_target,
                    'cover_bonus': cover_bonus,
                    'distance_safety': distance_safety_score
                })

        # Sort candidates by score
        candidates.sort(key=lambda x: -x['score'])
        agent_candidates[agent['agent_id']] = candidates
        print(f"DEBUG: Agent {agent['agent_id']} has {len(candidates)} candidate positions", file=sys.stderr)

    # Find the best combination of positions that avoids splash bomb violations
    best_positions = {}

    # Try to assign positions iteratively, checking for violations
    agent_ids = sorted(my_agents, key=lambda x: x['agent_id'])

    for agent in agent_ids:
        agent_id = agent['agent_id']
        candidates = agent_candidates.get(agent_id, [])

        if not candidates:
            # No candidates, stay at current position
            best_positions[agent_id] = (agent['x'], agent['y'])
            continue

        # Try each candidate position
        position_assigned = False
        for candidate in candidates:
            # Test this position
            test_positions = best_positions.copy()
            test_positions[agent_id] = candidate['pos']

            # Check for violations
            violations = check_position_safety(test_positions, threat_zones)

            if not violations or (force_aggression and len(violations) <= 1):
                # This position is safe, or acceptable under forced aggression
                best_positions[agent_id] = candidate['pos']
                position_assigned = True
                safety_note = " (ACCEPTING RISK DUE TO FORCED AGGRESSION)" if violations and force_aggression else ""
                print(f"DEBUG: Agent {agent_id} assigned position {candidate['pos']}, score: {candidate['score']}{safety_note}", file=sys.stderr)
                break

        if not position_assigned:
            # All positions have violations, use best candidate under forced aggression or current position
            if force_aggression and candidates:
                best_positions[agent_id] = candidates[0]['pos']  # Use best scoring position regardless of safety
                print(f"DEBUG: Agent {agent_id} FORCED to best position {candidates[0]['pos']} despite safety risks", file=sys.stderr)
            else:
                best_positions[agent_id] = (agent['x'], agent['y'])
                print(f"DEBUG: Agent {agent_id} no safe position found, staying at current", file=sys.stderr)

    # Final safety check
    final_violations = check_position_safety(best_positions, threat_zones)
    if final_violations:
        aggression_justification = " (ACCEPTABLE DUE TO FORCED AGGRESSION)" if force_aggression else ""
        print(f"DEBUG: WARNING - {len(final_violations)} splash violations remain in final positions{aggression_justification}", file=sys.stderr)
        for violation in final_violations:
            print(f"DEBUG: Violation - Enemy {violation['enemy_id']} can hit agents {violation['agents_at_risk']} at {violation['throw_target']}", file=sys.stderr)
    else:
        print(f"DEBUG: All positions are splash-safe", file=sys.stderr)

    # Convert to format expected by calling code
    final_positions = {}
    for agent in my_agents:
        agent_id = agent['agent_id']
        pos = best_positions.get(agent_id, (agent['x'], agent['y']))
        final_positions[agent_id] = {
            'pos': pos,
            'score': 100,  # Simplified since we prioritized safety
            'distance_to_target': manhattan_distance(pos[0], pos[1], target_enemy['x'], target_enemy['y']),
            'cover_bonus': 0,
            'splash_safety': 100 if not final_violations else 0,
            'distance_safety': 0
        }

    return final_positions, len(final_violations) > 0

def find_safe_shooting_positions(my_agents, target_enemy, enemy_agents, grid, width, height, agents_init, force_aggression=False, target_positions=None, rotation_plan=None):
    """Find positions where agents can shoot target while staying safe from splash bombs and considering front-line control."""
    # Use the new comprehensive splash-safe position finding with front-line bias
    return find_safe_positions_avoiding_splash(my_agents, target_enemy, enemy_agents, grid, width, height, agents_init, force_aggression, target_positions, rotation_plan)

def execute_smart_focus_fire_strategy(my_agents, enemy_agents, grid, width, height, agents_init, force_aggression=False, front_line_info=None, rotation_plan=None):
    """Execute smart focus fire with front-line control and agent rotation."""
    agent_commands = {}

    aggression_mode = "FORCED" if force_aggression else "NORMAL"
    print(f"DEBUG: Smart focus fire strategy with {len(my_agents)} agents, aggression: {aggression_mode}", file=sys.stderr)

    # Use front-line information if available
    if front_line_info:
        target_positions = front_line_info['target_positions']
        print(f"DEBUG: Using front-line control, target line: {front_line_info['target_line']}", file=sys.stderr)
    else:
        target_positions = {agent['agent_id']: (agent['x'], agent['y']) for agent in my_agents}

    # Apply rotation plan if available
    if rotation_plan:
        print(f"DEBUG: Rotation plan - Out: {len(rotation_plan['rotate_out'])}, In: {len(rotation_plan['rotate_in'])}", file=sys.stderr)

    if not enemy_agents:
        # No enemies, just hunker down
        for agent in my_agents:
            command = f"{agent['agent_id']};HUNKER_DOWN;MESSAGE No enemies"
            agent_commands[agent['agent_id']] = command
        return agent_commands

    # Find the nearest enemy to our group center
    center_x = sum(agent['x'] for agent in my_agents) / len(my_agents)
    center_y = sum(agent['y'] for agent in my_agents) / len(my_agents)

    # Prioritize enemies: nearest first, but prefer weak/exposed ones
    target_scores = []
    for enemy in enemy_agents:
        distance_to_center = manhattan_distance(center_x, center_y, enemy['x'], enemy['y'])
        weakness_score = enemy.get('wetness', 0)  # Prefer damaged enemies

        # Check how many of our agents can hit this enemy
        agents_in_range = 0
        for agent in my_agents:
            init_agent = next(init_a for init_a in agents_init if init_a['agent_id'] == agent['agent_id'])
            agent_distance = manhattan_distance(agent['x'], agent['y'], enemy['x'], enemy['y'])
            if agent_distance <= 2 * init_agent['optimal_range']:
                agents_in_range += 1

        # Score: prefer weak enemies we can hit with multiple agents, that are close
        score = weakness_score * 2 + agents_in_range * 10 - distance_to_center
        target_scores.append((enemy, score))

    target_scores.sort(key=lambda x: -x[1])
    target_enemy = target_scores[0][0]

    print(f"DEBUG: Target enemy {target_enemy['agent_id']} at ({target_enemy['x']}, {target_enemy['y']}), wetness: {target_enemy.get('wetness', 0)}", file=sys.stderr)

    # Find safe shooting positions (with aggression override and front-line bias)
    shooting_positions, high_threat = find_safe_shooting_positions(my_agents, target_enemy, enemy_agents, grid, width, height, agents_init, force_aggression, target_positions, rotation_plan)

    # Plan all moves first, then resolve collisions
    planned_moves = {}
    for agent in my_agents:
        agent_x, agent_y = agent['x'], agent['y']
        position_info = shooting_positions.get(agent['agent_id'])
        target_pos = position_info['pos'] if position_info else (agent_x, agent_y)



        if target_pos != (agent_x, agent_y):
            planned_moves[agent['agent_id']] = {
                'from': (agent_x, agent_y),
                'to': target_pos
            }

    # Debug: Show planned moves before collision resolution
    if planned_moves:
        print(f"DEBUG: Planned moves before collision resolution: {planned_moves}", file=sys.stderr)

    # Resolve move collisions
    resolved_moves, cancelled_moves = resolve_move_collisions(my_agents, planned_moves, grid, width, height)

    # Debug: Show results of collision resolution
    if cancelled_moves:
        print(f"DEBUG: Collision resolution cancelled {len(cancelled_moves)} moves", file=sys.stderr)
    else:
        print(f"DEBUG: No move collisions detected", file=sys.stderr)

    # Find alternative moves for cancelled agents
    for cancelled in cancelled_moves:
        agent_id = cancelled['agent_id']
        agent = next(a for a in my_agents if a['agent_id'] == agent_id)
        original_target = cancelled['cancelled_move']['to']

        alternative_pos = find_alternative_move(agent, original_target, grid, width, height, resolved_moves)

        if alternative_pos != (agent['x'], agent['y']):
            resolved_moves[agent_id] = {
                'from': (agent['x'], agent['y']),
                'to': alternative_pos
            }

    # Process each agent with collision-resolved moves
    for agent in sorted(my_agents, key=lambda x: x['agent_id']):
        command = f"{agent['agent_id']}"
        agent_x, agent_y = agent['x'], agent['y']
        init_agent = next(init_a for init_a in agents_init if init_a['agent_id'] == agent['agent_id'])

        # Get final position (after collision resolution)
        if agent['agent_id'] in resolved_moves:
            target_pos = resolved_moves[agent['agent_id']]['to']
        else:
            target_pos = (agent_x, agent_y)  # No move planned or move was cancelled

        target_distance = manhattan_distance(target_pos[0], target_pos[1], target_enemy['x'], target_enemy['y'])

        print(f"DEBUG: Agent {agent['agent_id']} at ({agent_x}, {agent_y}), target pos: {target_pos}, distance: {target_distance}", file=sys.stderr)

        # Check if this agent had a move collision
        was_cancelled = any(c['agent_id'] == agent['agent_id'] for c in cancelled_moves)
        if was_cancelled:
            print(f"DEBUG: Agent {agent['agent_id']} had move collision, using alternative position", file=sys.stderr)

        # Check if agent is critically damaged
        if agent['wetness'] >= 70:
            # Survival mode - prioritize safety
            if (agent_x, agent_y) != target_pos:
                move_x, move_y = target_pos
                command += f";MOVE {move_x} {move_y}"
                print(f"DEBUG: Agent {agent['agent_id']} critical health, moving to safety ({move_x}, {move_y})", file=sys.stderr)
            command += ";HUNKER_DOWN"

        # Check if we can shoot from current position
        elif agent['cooldown'] == 0:
            current_distance = manhattan_distance(agent_x, agent_y, target_enemy['x'], target_enemy['y'])

            if current_distance <= 2 * init_agent['optimal_range']:
                effective_wetness, _, _, _ = calculate_effective_wetness(
                    agent_x, agent_y, target_enemy, grid, width, height,
                    init_agent['soaking_power'], init_agent['optimal_range']
                )

                if effective_wetness > 0:
                    command += f";SHOOT {target_enemy['agent_id']}"
                    print(f"DEBUG: Agent {agent['agent_id']} shooting from current position, effective wetness: {effective_wetness}", file=sys.stderr)
                else:
                    # Move to better position and try to shoot
                    if (agent_x, agent_y) != target_pos:
                        move_x, move_y = target_pos
                        command += f";MOVE {move_x} {move_y}"

                        # Try shooting from new position
                        new_distance = manhattan_distance(move_x, move_y, target_enemy['x'], target_enemy['y'])
                        if new_distance <= 2 * init_agent['optimal_range']:
                            new_effective_wetness, _, _, _ = calculate_effective_wetness(
                                move_x, move_y, target_enemy, grid, width, height,
                                init_agent['soaking_power'], init_agent['optimal_range']
                            )

                            if new_effective_wetness > 0:
                                command += f";SHOOT {target_enemy['agent_id']}"
                                print(f"DEBUG: Agent {agent['agent_id']} moved and shooting, effective wetness: {new_effective_wetness}", file=sys.stderr)
                            else:
                                command += ";HUNKER_DOWN"
                        else:
                            command += ";HUNKER_DOWN"
                    else:
                        command += ";HUNKER_DOWN"
            else:
                # Too far to shoot, move closer
                if (agent_x, agent_y) != target_pos:
                    move_x, move_y = target_pos
                    command += f";MOVE {move_x} {move_y}"

                    # Try shooting from new position
                    new_distance = manhattan_distance(move_x, move_y, target_enemy['x'], target_enemy['y'])
                    if new_distance <= 2 * init_agent['optimal_range']:
                        new_effective_wetness, _, _, _ = calculate_effective_wetness(
                            move_x, move_y, target_enemy, grid, width, height,
                            init_agent['soaking_power'], init_agent['optimal_range']
                        )

                        if new_effective_wetness > 0:
                            command += f";SHOOT {target_enemy['agent_id']}"
                            print(f"DEBUG: Agent {agent['agent_id']} moved closer and shooting, effective wetness: {new_effective_wetness}", file=sys.stderr)
                        else:
                            command += ";HUNKER_DOWN"
                    else:
                        command += ";HUNKER_DOWN"
                else:
                    command += ";HUNKER_DOWN"

        # On cooldown, just position
        else:
            if (agent_x, agent_y) != target_pos:
                move_x, move_y = target_pos
                command += f";MOVE {move_x} {move_y}"
                print(f"DEBUG: Agent {agent['agent_id']} on cooldown, moving to position ({move_x}, {move_y})", file=sys.stderr)
            command += ";HUNKER_DOWN"

        # Add status message
        threat_indicator = "!" if high_threat else ""
        status = f"SF{threat_indicator} W:{agent['wetness']} D:{target_distance}"
        command += f";MESSAGE {status}"

        agent_commands[agent['agent_id']] = command

    return agent_commands

def calculate_territory_control_value(x, y, my_agents, enemy_agents):
    """Calculate how much this position helps with territory control."""
    control_value = 0

    # Distance to center of map (prefer central positions)
    center_distance = abs(x - 8) + abs(y - 6)  # Assuming roughly 16x12 map
    control_value += max(0, 20 - center_distance)

    # Distance to nearest enemy (prefer positions that threaten enemies)
    if enemy_agents:
        min_enemy_distance = min(manhattan_distance(x, y, e['x'], e['y']) for e in enemy_agents)
        control_value += max(0, 15 - min_enemy_distance)

    # Distance to nearest friendly (avoid clustering too much)
    if my_agents:
        min_friendly_distance = min(manhattan_distance(x, y, a['x'], a['y']) for a in my_agents)
        if min_friendly_distance < 3:
            control_value -= 10  # Penalty for clustering

    return control_value

def find_best_position(agent, grid, width, height, my_agents, enemy_agents, prioritize_cover=True):
    """Find the best position considering both cover and territory control."""
    current_x, current_y = agent['x'], agent['y']
    adjacent_tiles = get_adjacent_tiles(current_x, current_y, width, height)

    best_score = -1000
    best_pos = None
    best_cover = 0

    for x, y in adjacent_tiles:
        # Skip if the tile is a cover tile (impassable)
        tile = next((t for t in grid if t['x'] == x and t['y'] == y), None)
        if tile and tile['tile_type'] > 0:
            continue  # Cannot move onto cover tiles

        # Check for other agents on this tile
        occupied = any(a['x'] == x and a['y'] == y for a in my_agents + enemy_agents if a != agent)
        if occupied:
            continue

        # Calculate cover value
        cover_adjacent = get_adjacent_tiles(x, y, width, height)
        max_cover = 0
        for cx, cy in cover_adjacent:
            cover_tile = next((t for t in grid if t['x'] == cx and t['y'] == cy), None)
            if cover_tile and cover_tile['tile_type'] > max_cover:
                max_cover = cover_tile['tile_type']

        # Calculate territory control value
        territory_value = calculate_territory_control_value(x, y, my_agents, enemy_agents)

        # Combined score
        if prioritize_cover:
            score = max_cover * 100 + territory_value
        else:
            score = territory_value + max_cover * 20

        if score > best_score:
            best_score = score
            best_pos = (x, y)
            best_cover = max_cover

    return best_pos, best_cover

def find_closest_cover_position(agent, grid, width, height):
    """Find the closest tile adjacent to the highest cover (one move away)."""
    current_x, current_y = agent['x'], agent['y']
    adjacent_tiles = get_adjacent_tiles(current_x, current_y, width, height)
    best_cover = 0
    best_pos = None
    for x, y in adjacent_tiles:
        # Skip if the tile is a cover tile (impassable)
        tile = next((t for t in grid if t['x'] == x and t['y'] == y), None)
        if tile and tile['tile_type'] > 0:
            continue  # Cannot move onto cover tiles
        # Check adjacent tiles for cover
        cover_adjacent = get_adjacent_tiles(x, y, width, height)
        max_cover = 0
        for cx, cy in cover_adjacent:
            cover_tile = next((t for t in grid if t['x'] == cx and t['y'] == cy), None)
            if cover_tile and cover_tile['tile_type'] > max_cover:
                max_cover = cover_tile['tile_type']
        if max_cover > best_cover:
            best_cover = max_cover
            best_pos = (x, y)
        elif max_cover == best_cover and best_pos is None:
            best_pos = (x, y)  # Default to first valid position if tied
    return best_pos, best_cover

def get_effective_cover_value(shooter_x, shooter_y, enemy, grid, width, height):
    """Calculate effective cover for an enemy from the shooter's perspective."""
    enemy_x, enemy_y = enemy['x'], enemy['y']
    adjacent = get_adjacent_tiles(enemy_x, enemy_y, width, height)
    max_cover = 0
    cover_positions = []
    
    # If shooter and enemy are adjacent to the same cover, cover is ignored
    shooter_adjacent = get_adjacent_tiles(shooter_x, shooter_y, width, height)
    for x, y in adjacent:
        tile = next((t for t in grid if t['x'] == x and t['y'] == y), None)
        if tile and tile['tile_type'] > 0:
            if (x, y) in shooter_adjacent:
                continue  # Cover ignored if both are adjacent
            # Check if the cover tile lies directly on the shot path
            dx = enemy_x - shooter_x
            dy = enemy_y - shooter_y
            is_between = False
            if dx != 0 or dy != 0:
                if abs(dx) >= abs(dy):  # Primarily horizontal shot
                    if (dx > 0 and x < enemy_x and x >= shooter_x and y == enemy_y) or \
                       (dx < 0 and x > enemy_x and x <= shooter_x and y == enemy_y):
                        is_between = True
                else:  # Primarily vertical shot
                    if (dy > 0 and y < enemy_y and y >= shooter_y and x == enemy_x) or \
                       (dy < 0 and y > enemy_y and y <= shooter_y and x == enemy_x):
                        is_between = True
            if is_between:
                max_cover = max(max_cover, tile['tile_type'])
                cover_positions.append((x, y, tile['tile_type']))
    
    return max_cover, cover_positions

def calculate_effective_wetness(shooter_x, shooter_y, enemy, grid, width, height, soaking_power, optimal_range):
    """Calculate the effective wetness applied to an enemy."""
    distance = manhattan_distance(shooter_x, shooter_y, enemy['x'], enemy['y'])
    cover_value, cover_positions = get_effective_cover_value(shooter_x, shooter_y, enemy, grid, width, height)
    
    # Cover multiplier
    cover_multiplier = 1.0
    if cover_value == 1:
        cover_multiplier = 0.5  # Low cover: 50% damage
    elif cover_value == 2:
        cover_multiplier = 0.25  # High cover: 25% damage
    
    # Distance multiplier
    if distance > 2 * optimal_range:
        distance_multiplier = 0.0  # Shots beyond 2 * optimal_range fail
    elif distance > optimal_range:
        distance_multiplier = 0.5  # Reduced damage beyond optimal_range
    else:
        distance_multiplier = 1.0  # Full damage within optimal_range
    
    # Effective wetness
    effective_wetness = soaking_power * cover_multiplier * distance_multiplier
    return effective_wetness, cover_value, distance, cover_positions

def find_best_target_enemy(shooter_x, shooter_y, enemies, grid, width, height, optimal_range, soaking_power, taken_enemies):
    """Rank enemies by effective wetness and filter by distance."""
    if not enemies:
        return None
    # Rank all enemies by effective wetness
    enemy_data = []
    for enemy in enemies:
        if enemy['agent_id'] in taken_enemies:
            continue
        wetness, cover_value, distance, cover_pos = calculate_effective_wetness(
            shooter_x, shooter_y, enemy, grid, width, height, soaking_power, optimal_range
        )
        # Filter by distance: within (0.5 * map_width) + 1
        max_distance = (width * 0.5) + 1
        if distance <= max_distance:
            enemy_data.append((enemy, wetness, cover_value, distance, cover_pos))
    
    if not enemy_data:
        return None
    
    # Sort by effective wetness (descending), then distance (ascending)
    enemy_data.sort(key=lambda x: (-x[1], x[3]))
    print(f"DEBUG: Agent at ({shooter_x}, {shooter_y}) enemy options: {[(e[0]['agent_id'], e[1], e[2], e[3], e[4]) for e in enemy_data]}", file=sys.stderr)
    return enemy_data[0][0] if enemy_data else None

# Read initialization input
my_id = int(input())
agent_data_count = int(input())
agents_init = []
for _ in range(agent_data_count):
    agent_id, player, shoot_cooldown, optimal_range, soaking_power, splash_bombs = map(int, input().split())
    agents_init.append({
        'agent_id': agent_id,
        'player': player,
        'shoot_cooldown': shoot_cooldown,
        'optimal_range': optimal_range,
        'soaking_power': soaking_power,
        'splash_bombs': splash_bombs
    })
width, height = map(int, input().split())
grid = []
for i in range(height):
    inputs = input().split()
    for j in range(width):
        x = int(inputs[3*j])
        y = int(inputs[3*j+1])
        tile_type = int(inputs[3*j+2])
        grid.append({'x': x, 'y': y, 'tile_type': tile_type})

# Main game loop
while True:
    try:
        agent_count = int(input())
        agents = []
        for _ in range(agent_count):
            agent_id, x, y, cooldown, splash_bombs, wetness = map(int, input().split())
            agents.append({
                'agent_id': agent_id,
                'x': x,
                'y': y,
                'cooldown': cooldown,
                'splash_bombs': splash_bombs,
                'wetness': wetness
            })
        my_agent_count = int(input())

        # Find my agents and enemy agents
        my_agents = [agent for agent in agents if any(init_agent['agent_id'] == agent['agent_id'] and init_agent['player'] == my_id for init_agent in agents_init)]
        enemy_agents = [agent for agent in agents if any(init_agent['agent_id'] == agent['agent_id'] and init_agent['player'] != my_id for init_agent in agents_init)]

        # Debug: Print current state
        print(f"DEBUG: Turn - My agents: {[(a['agent_id'], a['x'], a['y'], a['splash_bombs'], a['wetness']) for a in my_agents]}", file=sys.stderr)
        print(f"DEBUG: Turn - Enemy agents: {[(a['agent_id'], a['x'], a['y'], a['wetness']) for a in enemy_agents]}", file=sys.stderr)
        print(f"DEBUG: Full game mode - territory control and elimination", file=sys.stderr)

        # Calculate predictive territory control and front line management
        front_line_info = calculate_front_line_control(my_agents, enemy_agents, grid, width, height)
        my_control = front_line_info['my_control']
        enemy_control = front_line_info['enemy_control']
        advantage = front_line_info['advantage']
        front_line = front_line_info['front_line']
        target_positions = front_line_info['target_positions']

        # Assess agent health and rotation needs
        rotation_plan = assess_agent_rotation_needs(my_agents, enemy_agents, agents_init, front_line)

        # Critical: Check if we're at risk of 600-point deficit loss
        # Use predictive front-line control rather than just current territory
        CRITICAL_TERRITORY_DEFICIT = 25  # If enemy controls 25+ more tiles, go ultra-aggressive
        MODERATE_TERRITORY_DEFICIT = 10  # If enemy controls 10+ more tiles, be more aggressive

        is_critical_deficit = advantage < -CRITICAL_TERRITORY_DEFICIT
        is_moderate_deficit = advantage < -MODERATE_TERRITORY_DEFICIT

        print(f"DEBUG: Front-line control - Us: {my_control}, Enemy: {enemy_control}, Advantage: {advantage}", file=sys.stderr)
        print(f"DEBUG: Agent rotation plan: {rotation_plan}", file=sys.stderr)
        if is_critical_deficit:
            print(f"DEBUG: CRITICAL DEFICIT DETECTED! Enemy controls {-advantage} more tiles - forcing aggressive strategy", file=sys.stderr)
        elif is_moderate_deficit:
            print(f"DEBUG: Moderate deficit detected - enemy controls {-advantage} more tiles", file=sys.stderr)

        # Strategy selection with adaptive override based on score deficit risk
        BASE_STRATEGY = "smart_focus_fire"  # Options: "balanced", "coordinated_focus_fire", "defensive_control", "smart_focus_fire"

        # Override strategy if we're at risk of 600-point deficit loss
        if is_critical_deficit:
            # Force ultra-aggressive strategy to regain territory
            ACTIVE_STRATEGY = "coordinated_focus_fire"
            print(f"DEBUG: STRATEGY OVERRIDE - Using coordinated_focus_fire due to critical deficit", file=sys.stderr)
        elif is_moderate_deficit:
            # Use aggressive strategy but with some safety
            ACTIVE_STRATEGY = "smart_focus_fire"
            print(f"DEBUG: STRATEGY ADJUSTMENT - Using smart_focus_fire with increased aggression", file=sys.stderr)
        else:
            ACTIVE_STRATEGY = BASE_STRATEGY

        if ACTIVE_STRATEGY == "balanced":
            agent_commands = execute_balanced_strategy(my_agents, enemy_agents, grid, width, height, agents_init)
        elif ACTIVE_STRATEGY == "coordinated_focus_fire":
            agent_commands = execute_coordinated_focus_fire_strategy(my_agents, enemy_agents, grid, width, height, agents_init)
        elif ACTIVE_STRATEGY == "defensive_control":
            agent_commands = execute_defensive_control_strategy(my_agents, enemy_agents, grid, width, height, agents_init)
        elif ACTIVE_STRATEGY == "smart_focus_fire":
            # Pass deficit information and front-line data to strategy for adaptive behavior
            agent_commands = execute_smart_focus_fire_strategy(my_agents, enemy_agents, grid, width, height, agents_init,
                                                             force_aggression=is_moderate_deficit or is_critical_deficit,
                                                             front_line_info=front_line_info, rotation_plan=rotation_plan)
        else:
            # Fallback to balanced strategy
            agent_commands = execute_balanced_strategy(my_agents, enemy_agents, grid, width, height, agents_init)

        # Output commands in agent ID order to ensure consistent output
        for my_agent in sorted(my_agents, key=lambda x: x['agent_id']):
            print(agent_commands[my_agent['agent_id']])

    except EOFError:
        # Game ended
        break
    except Exception as e:
        print(f"ERROR: {e}", file=sys.stderr)
        break